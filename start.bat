@echo off
chcp 65001 >nul
title 智能推荐系统 - 启动器

echo.
echo ========================================
echo 🤖 智能推荐系统 - Windows启动器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

:: 检查是否存在虚拟环境
if exist "venv\Scripts\activate.bat" (
    echo.
    echo 🔧 激活虚拟环境...
    call venv\Scripts\activate.bat
)

:: 检查依赖是否安装
echo.
echo 🔍 检查依赖包...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  依赖包未安装，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装完成
) else (
    echo ✅ 依赖包已安装
)

:: 检查Docker是否可用
echo.
echo 🐳 检查Docker状态...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Docker未安装或未运行
    echo 将使用本地Python模式启动
    set USE_DOCKER=false
) else (
    echo ✅ Docker可用
    set USE_DOCKER=true
)

:: 显示启动选项
echo.
echo ========================================
echo 请选择启动方式:
echo ========================================
echo 1. 🚀 本地启动 (推荐用于开发)
echo 2. 🐳 Docker启动 (推荐用于生产)
echo 3. 📦 一键安装向导
echo 4. 🧪 运行系统测试
echo 5. ❓ 查看帮助
echo 6. 🚪 退出
echo ========================================

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" goto local_start
if "%choice%"=="2" goto docker_start
if "%choice%"=="3" goto install_wizard
if "%choice%"=="4" goto run_tests
if "%choice%"=="5" goto show_help
if "%choice%"=="6" goto exit
echo ❌ 无效选择
pause
goto start

:local_start
echo.
echo 🚀 启动本地服务...
python start.py
goto end

:docker_start
if "%USE_DOCKER%"=="false" (
    echo ❌ Docker不可用，请选择本地启动
    pause
    goto start
)
echo.
echo 🐳 启动Docker服务...
python docker_deploy.py run
goto end

:install_wizard
echo.
echo 📦 启动安装向导...
python install.py
goto end

:run_tests
echo.
echo 🧪 运行系统测试...
python test_system.py
pause
goto start

:show_help
echo.
echo ========================================
echo 📖 帮助信息
echo ========================================
echo.
echo 🎯 项目功能:
echo   • 智能推荐算法
echo   • 用户反馈学习
echo   • 局域网设备发现
echo   • 实时网络状态监控
echo.
echo 🔗 访问地址:
echo   • 本地: http://localhost:5000
echo   • 局域网: http://[你的IP]:5000
echo.
echo 📁 重要文件:
echo   • start.py - 启动脚本
echo   • install.py - 安装向导
echo   • docker_deploy.py - Docker部署
echo   • test_system.py - 系统测试
echo.
echo 🆘 故障排除:
echo   • 端口被占用: 修改.env文件中的PORT
echo   • 依赖问题: 运行 pip install -r requirements.txt
echo   • 网络问题: 检查防火墙设置
echo.
echo 📞 获取帮助:
echo   • 查看README.md文档
echo   • 运行系统测试检查问题
echo.
pause
goto start

:exit
echo.
echo 👋 再见！
exit /b 0

:end
echo.
echo 🎉 操作完成
pause
