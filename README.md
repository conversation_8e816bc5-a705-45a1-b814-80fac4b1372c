# 🤖 智能推荐系统 - 局域网版

一个基于机器学习的智能推荐系统，支持局域网部署和设备发现功能。

## ✨ 功能特性

### 🎯 智能推荐
- **个性化推荐**: 基于用户行为和偏好的智能推荐算法
- **内容过滤**: TF-IDF向量化和余弦相似度计算
- **协同过滤**: 基于用户相似性的推荐
- **混合策略**: 结合多种推荐算法的混合推荐
- **实时学习**: 根据用户反馈动态调整推荐结果

### 🌐 局域网部署
- **设备发现**: 自动扫描和发现局域网内的设备
- **网络监控**: 实时监控网络状态和服务
- **多平台支持**: 支持Windows、Linux、macOS
- **端口扫描**: 检测设备开放的服务端口
- **零配置**: 开箱即用的局域网部署

### 🎨 用户界面
- **响应式设计**: 适配桌面和移动设备
- **实时交互**: Ajax异步请求，无需刷新页面
- **直观操作**: 简洁美观的用户界面
- **即时反馈**: 实时显示推荐结果和网络状态

## 🚀 快速开始

### 方式一：一键安装 (推荐)
```bash
python install.py
```
安装向导会引导您选择最适合的安装方式。

### 方式二：手动安装

#### 1. 环境要求
- Python 3.7+
- 局域网环境
- 现代浏览器
- Docker (可选，用于容器化部署)

#### 2. 本地安装
```bash
# 安装依赖
pip install -r requirements.txt

# 启动系统
python start.py
```

#### 3. Docker安装
```bash
# 构建并运行
python docker_deploy.py run

# 或使用Docker Compose
python docker_deploy.py compose-up
```

#### 4. 访问系统
- 本地访问: http://localhost:5000
- 局域网访问: http://[你的IP]:5000

## 📁 项目结构

```
recommendation-system/
├── app.py                 # Flask主应用
├── recommendation_agent.py # 推荐算法核心
├── network_manager.py     # 网络管理模块
├── start.py              # 启动脚本
├── install.py            # 一键安装脚本
├── docker_deploy.py      # Docker部署脚本
├── test_system.py        # 系统测试脚本
├── requirements.txt      # 依赖包列表
├── Dockerfile           # Docker镜像构建文件
├── docker-compose.yml   # Docker Compose配置
├── .dockerignore        # Docker忽略文件
├── .env                 # 环境配置
├── README.md           # 说明文档
├── templates/          # 前端模板
│   └── index.html     # 主页面
├── data/              # 数据存储
│   ├── user_profiles.json
│   ├── item_features.json
│   └── interaction_history.json
└── logs/              # 日志文件
```

## 🔧 配置说明

### 环境变量 (.env)
```bash
# 服务配置
HOST=0.0.0.0          # 监听地址 (0.0.0.0 允许局域网访问)
PORT=5000             # 服务端口
DEBUG=False           # 调试模式

# 网络配置
NETWORK_SCAN_TIMEOUT=5    # 网络扫描超时时间
MAX_SCAN_THREADS=50       # 最大扫描线程数

# 安全配置
SECRET_KEY=your_secret_key_here
CORS_ORIGINS=*            # 跨域访问控制
```

## 📊 API接口

### 获取推荐
```http
POST /api/recommend
Content-Type: application/json

{
    "user_id": "user_001",
    "query": "机器学习",
    "preferences": {
        "category": "编程书籍"
    }
}
```

### 提交反馈
```http
POST /api/feedback
Content-Type: application/json

{
    "user_id": "user_001",
    "item_id": "item_1",
    "rating": 5,
    "type": "rating"
}
```

### 网络状态
```http
GET /api/network/status
```

### 设备发现
```http
GET /api/network/discover
```

## 🐳 Docker部署

### 1. 基础Docker命令
```bash
# 构建镜像
python docker_deploy.py build

# 运行容器
python docker_deploy.py run

# 查看状态
python docker_deploy.py status

# 查看日志
python docker_deploy.py logs

# 停止容器
python docker_deploy.py stop
```

### 2. Docker Compose部署
```bash
# 启动所有服务 (包括Redis缓存)
python docker_deploy.py compose-up

# 停止所有服务
python docker_deploy.py compose-down
```

### 3. 镜像分发
```bash
# 导出镜像
python docker_deploy.py export --file recommendation-system.tar

# 在其他机器上加载镜像
python docker_deploy.py load --file recommendation-system.tar
```

## 🛠️ 高级配置

### 1. 防火墙设置
确保防火墙允许端口5000的入站连接:

**Windows:**
```cmd
netsh advfirewall firewall add rule name="Recommendation System" dir=in action=allow protocol=TCP localport=5000
```

**Linux:**
```bash
sudo ufw allow 5000
```

### 2. 生产环境部署

**使用Docker (推荐):**
```bash
python docker_deploy.py compose-up
```

**使用Gunicorn:**
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

### 3. 数据持久化
系统自动将用户数据保存到`data/`目录:
- `user_profiles.json`: 用户档案
- `item_features.json`: 物品特征
- `interaction_history.json`: 交互历史

Docker部署时，数据会自动挂载到宿主机，确保数据持久化。

## 🔍 使用指南

### 1. 获取推荐
1. 输入用户ID (如: user_001)
2. 输入搜索查询 (可选)
3. 选择偏好类别 (可选)
4. 点击"获取推荐"按钮

### 2. 提交反馈
1. 在推荐结果中点击星级评分
2. 系统自动学习用户偏好
3. 后续推荐会更加精准

### 3. 网络管理
1. 点击"网络状态"查看当前网络信息
2. 点击"扫描设备"发现局域网设备
3. 查看设备IP、主机名和在线状态

## 🎯 推荐算法

### 1. 内容过滤
- 使用TF-IDF向量化文本特征
- 计算余弦相似度
- 基于物品内容推荐

### 2. 协同过滤
- 分析用户行为模式
- 找到相似用户群体
- 推荐相似用户喜欢的物品

### 3. 混合策略
- 内容推荐权重: 70%
- 协同过滤权重: 30%
- 动态调整推荐结果

## 🌐 网络功能

### 1. 设备发现
- 自动扫描局域网段 (如: ***********/24)
- 多线程并发扫描提高效率
- 检测设备在线状态和开放端口

### 2. 服务监控
- 监控本机运行的网络服务
- 检测常见端口 (HTTP, HTTPS, SSH等)
- 实时显示服务状态

## 🔒 安全注意事项

1. **网络安全**: 仅在可信的局域网环境中使用
2. **数据保护**: 用户数据存储在本地，注意备份
3. **访问控制**: 生产环境建议配置访问控制
4. **更新维护**: 定期更新依赖包和系统

## 🐛 故障排除

### 常见问题

**Q: 无法访问局域网地址**
A: 检查防火墙设置，确保允许端口5000的访问

**Q: 设备扫描失败**
A: 确保网络连接正常，某些网络可能禁止扫描

**Q: 推荐结果为空**
A: 检查数据文件是否存在，尝试重新初始化数据

**Q: 端口被占用**
A: 修改.env文件中的PORT配置，使用其他端口

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看启动日志
python start.py
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License - 详见LICENSE文件

## 📞 联系方式

如有问题或建议，请通过以下方式联系:
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**享受智能推荐的乐趣！** 🎉
