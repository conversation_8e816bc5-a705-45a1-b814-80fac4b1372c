#!/bin/bash

# 智能推荐系统 - Linux/macOS启动器

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示标题
echo -e "${BLUE}"
echo "========================================"
echo "🤖 智能推荐系统 - Linux/macOS启动器"
echo "========================================"
echo -e "${NC}"

# 检查Python
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
    else
        echo -e "${RED}❌ Python未安装${NC}"
        echo "请先安装Python 3.7或更高版本"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Python已安装${NC}"
    $PYTHON_CMD --version
}

# 检查依赖
check_dependencies() {
    echo -e "${CYAN}🔍 检查依赖包...${NC}"
    
    if $PYTHON_CMD -c "import flask" &> /dev/null; then
        echo -e "${GREEN}✅ 依赖包已安装${NC}"
    else
        echo -e "${YELLOW}⚠️  依赖包未安装，正在安装...${NC}"
        $PYTHON_CMD -m pip install -r requirements.txt
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ 依赖安装完成${NC}"
        else
            echo -e "${RED}❌ 依赖安装失败${NC}"
            exit 1
        fi
    fi
}

# 检查Docker
check_docker() {
    if command -v docker &> /dev/null; then
        if docker info &> /dev/null; then
            echo -e "${GREEN}✅ Docker可用${NC}"
            USE_DOCKER=true
        else
            echo -e "${YELLOW}⚠️  Docker未运行${NC}"
            USE_DOCKER=false
        fi
    else
        echo -e "${YELLOW}⚠️  Docker未安装${NC}"
        USE_DOCKER=false
    fi
}

# 显示菜单
show_menu() {
    echo
    echo "========================================"
    echo "请选择启动方式:"
    echo "========================================"
    echo "1. 🚀 本地启动 (推荐用于开发)"
    echo "2. 🐳 Docker启动 (推荐用于生产)"
    echo "3. 📦 一键安装向导"
    echo "4. 🧪 运行系统测试"
    echo "5. ❓ 查看帮助"
    echo "6. 🚪 退出"
    echo "========================================"
}

# 本地启动
local_start() {
    echo -e "${CYAN}🚀 启动本地服务...${NC}"
    $PYTHON_CMD start.py
}

# Docker启动
docker_start() {
    if [ "$USE_DOCKER" = false ]; then
        echo -e "${RED}❌ Docker不可用，请选择本地启动${NC}"
        return 1
    fi
    
    echo -e "${CYAN}🐳 启动Docker服务...${NC}"
    $PYTHON_CMD docker_deploy.py run
}

# 安装向导
install_wizard() {
    echo -e "${CYAN}📦 启动安装向导...${NC}"
    $PYTHON_CMD install.py
}

# 运行测试
run_tests() {
    echo -e "${CYAN}🧪 运行系统测试...${NC}"
    $PYTHON_CMD test_system.py
    echo
    read -p "按回车键继续..."
}

# 显示帮助
show_help() {
    echo
    echo "========================================"
    echo "📖 帮助信息"
    echo "========================================"
    echo
    echo "🎯 项目功能:"
    echo "  • 智能推荐算法"
    echo "  • 用户反馈学习"
    echo "  • 局域网设备发现"
    echo "  • 实时网络状态监控"
    echo
    echo "🔗 访问地址:"
    echo "  • 本地: http://localhost:5000"
    echo "  • 局域网: http://[你的IP]:5000"
    echo
    echo "📁 重要文件:"
    echo "  • start.py - 启动脚本"
    echo "  • install.py - 安装向导"
    echo "  • docker_deploy.py - Docker部署"
    echo "  • test_system.py - 系统测试"
    echo
    echo "🆘 故障排除:"
    echo "  • 端口被占用: 修改.env文件中的PORT"
    echo "  • 依赖问题: 运行 pip install -r requirements.txt"
    echo "  • 权限问题: 使用sudo或检查文件权限"
    echo "  • 网络问题: 检查防火墙设置"
    echo
    echo "📞 获取帮助:"
    echo "  • 查看README.md文档"
    echo "  • 运行系统测试检查问题"
    echo
    read -p "按回车键继续..."
}

# 主函数
main() {
    # 检查系统要求
    check_python
    check_dependencies
    check_docker
    
    while true; do
        show_menu
        read -p "请输入选择 (1-6): " choice
        
        case $choice in
            1)
                local_start
                break
                ;;
            2)
                docker_start
                if [ $? -eq 0 ]; then
                    break
                fi
                ;;
            3)
                install_wizard
                break
                ;;
            4)
                run_tests
                ;;
            5)
                show_help
                ;;
            6)
                echo -e "${CYAN}👋 再见！${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ 无效选择${NC}"
                ;;
        esac
    done
    
    echo -e "${GREEN}🎉 操作完成${NC}"
}

# 运行主函数
main
