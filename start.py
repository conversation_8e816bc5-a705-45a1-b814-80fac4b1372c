#!/usr/bin/env python3
"""
智能推荐系统启动脚本
支持局域网部署和设备发现
支持Docker容器化部署
"""

import os
import sys
import socket
import webbrowser
import time
import threading
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask', 'flask_cors', 'numpy', 'pandas', 
        'scikit-learn', 'requests', 'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_port(port):
    """检查端口是否可用"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
            sock.bind(('', port))
            return True
    except OSError:
        return False

def get_local_ip():
    """获取本机IP地址"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            s.connect(("*******", 80))
            return s.getsockname()[0]
    except Exception:
        return "127.0.0.1"

def create_directories():
    """创建必要的目录"""
    directories = ['data', 'logs', 'templates']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 目录结构已创建")

def check_docker():
    """检查Docker是否可用"""
    try:
        result = subprocess.run(['docker', '--version'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            return True
    except FileNotFoundError:
        pass
    return False

def display_startup_info(host, port):
    """显示启动信息"""
    local_ip = get_local_ip()
    docker_available = check_docker()

    print("\n" + "="*60)
    print("🚀 智能推荐系统已启动!")
    print("="*60)
    print(f"📍 本地访问: http://localhost:{port}")
    print(f"🌐 局域网访问: http://{local_ip}:{port}")
    print(f"💻 主机IP: {local_ip}")
    print(f"🔧 调试模式: {'开启' if os.getenv('DEBUG', 'False').lower() == 'true' else '关闭'}")
    if docker_available:
        print("🐳 Docker: 可用")
    print("="*60)
    print("📋 功能特性:")
    print("   • 智能推荐算法")
    print("   • 用户反馈学习")
    print("   • 局域网设备发现")
    print("   • 实时网络状态监控")
    if docker_available:
        print("   • Docker容器化部署")
    print("="*60)
    print("🔗 API端点:")
    print(f"   • 推荐接口: http://{local_ip}:{port}/api/recommend")
    print(f"   • 反馈接口: http://{local_ip}:{port}/api/feedback")
    print(f"   • 网络状态: http://{local_ip}:{port}/api/network/status")
    print(f"   • 设备发现: http://{local_ip}:{port}/api/network/discover")
    print("="*60)
    if docker_available:
        print("🐳 Docker部署选项:")
        print("   • 构建镜像: python docker_deploy.py build")
        print("   • 运行容器: python docker_deploy.py run")
        print("   • 使用Compose: python docker_deploy.py compose-up")
        print("   • 导出镜像: python docker_deploy.py export")
        print("="*60)
    print("⚠️  注意事项:")
    print("   • 确保防火墙允许端口访问")
    print("   • 局域网内其他设备可通过IP地址访问")
    print("   • 按 Ctrl+C 停止服务")
    if docker_available:
        print("   • 推荐使用Docker进行生产部署")
    print("="*60)

def open_browser(url, delay=2):
    """延迟打开浏览器"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 已在浏览器中打开: {url}")
    except Exception as e:
        print(f"⚠️  无法自动打开浏览器: {e}")

def main():
    """主函数"""
    print("🤖 智能推荐系统 - 局域网版")
    print("正在初始化...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 创建目录
    create_directories()
    
    # 获取配置
    from dotenv import load_dotenv
    load_dotenv()
    
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    # 检查端口
    if not check_port(port):
        print(f"❌ 端口 {port} 已被占用，请修改 .env 文件中的 PORT 配置")
        sys.exit(1)
    
    # 显示启动信息
    display_startup_info(host, port)
    
    # 在新线程中打开浏览器
    local_ip = get_local_ip()
    browser_thread = threading.Thread(
        target=open_browser, 
        args=(f"http://{local_ip}:{port}",)
    )
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动Flask应用
    try:
        from app import app
        app.run(host=host, port=port, debug=debug, threaded=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
