<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能推荐系统 - 局域网版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #4facfe;
            padding-bottom: 10px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        
        .input-group input, .input-group textarea, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus, .input-group textarea:focus, .input-group select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: transform 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .recommendations {
            margin-top: 20px;
        }
        
        .recommendation-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s;
        }
        
        .recommendation-item:hover {
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .recommendation-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .recommendation-meta {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 8px;
        }
        
        .recommendation-description {
            color: #555;
            line-height: 1.5;
        }
        
        .rating {
            display: inline-block;
            margin-top: 10px;
        }
        
        .rating button {
            background: #ddd;
            border: none;
            padding: 5px 10px;
            margin-right: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .rating button.active {
            background: #ffc107;
            color: white;
        }
        
        .network-info {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .device-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .device-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .device-status {
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-online {
            background: #d4edda;
            color: #155724;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 智能推荐系统</h1>
            <p>基于AI的个性化推荐 + 局域网部署</p>
        </div>
        
        <div class="main-content">
            <!-- 推荐系统部分 -->
            <div class="section">
                <h2>📚 智能推荐</h2>
                
                <div class="input-group">
                    <label for="userId">用户ID:</label>
                    <input type="text" id="userId" placeholder="输入您的用户ID" value="user_001">
                </div>
                
                <div class="input-group">
                    <label for="query">搜索查询:</label>
                    <input type="text" id="query" placeholder="输入您想要的内容...">
                </div>
                
                <div class="input-group">
                    <label for="category">偏好类别:</label>
                    <select id="category">
                        <option value="">选择类别</option>
                        <option value="编程书籍">编程书籍</option>
                        <option value="数据科学">数据科学</option>
                        <option value="Web开发">Web开发</option>
                        <option value="云计算">云计算</option>
                    </select>
                </div>
                
                <button class="btn" onclick="getRecommendations()">获取推荐</button>
                <button class="btn btn-secondary" onclick="clearRecommendations()">清空结果</button>
                
                <div id="recommendations" class="recommendations"></div>
            </div>
            
            <!-- 网络管理部分 -->
            <div class="section">
                <h2>🌐 网络管理</h2>
                
                <button class="btn" onclick="getNetworkStatus()">网络状态</button>
                <button class="btn btn-secondary" onclick="discoverDevices()">扫描设备</button>
                
                <div id="networkInfo" class="network-info" style="display: none;"></div>
                <div id="deviceList" class="device-list"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentRecommendations = [];
        
        // 获取推荐
        async function getRecommendations() {
            const userId = document.getElementById('userId').value;
            const query = document.getElementById('query').value;
            const category = document.getElementById('category').value;
            
            if (!userId) {
                alert('请输入用户ID');
                return;
            }
            
            const recommendationsDiv = document.getElementById('recommendations');
            recommendationsDiv.innerHTML = '<div class="loading">正在获取推荐...</div>';
            
            try {
                const response = await fetch('/api/recommend', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        query: query,
                        preferences: {
                            category: category
                        }
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    currentRecommendations = data.recommendations;
                    displayRecommendations(data.recommendations);
                } else {
                    recommendationsDiv.innerHTML = `<div class="error">错误: ${data.message}</div>`;
                }
            } catch (error) {
                recommendationsDiv.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 显示推荐结果
        function displayRecommendations(recommendations) {
            const recommendationsDiv = document.getElementById('recommendations');
            
            if (recommendations.length === 0) {
                recommendationsDiv.innerHTML = '<div class="loading">暂无推荐结果</div>';
                return;
            }
            
            let html = '';
            recommendations.forEach((rec, index) => {
                html += `
                    <div class="recommendation-item">
                        <div class="recommendation-title">${rec.title}</div>
                        <div class="recommendation-meta">
                            类别: ${rec.category} | 评分: ${rec.rating}/5 | 价格: ¥${rec.price}
                        </div>
                        <div class="recommendation-description">${rec.description}</div>
                        <div class="rating">
                            评价: 
                            ${[1,2,3,4,5].map(rating => 
                                `<button onclick="submitFeedback('${rec.item_id}', ${rating})">${rating}⭐</button>`
                            ).join('')}
                        </div>
                    </div>
                `;
            });
            
            recommendationsDiv.innerHTML = html;
        }
        
        // 提交反馈
        async function submitFeedback(itemId, rating) {
            const userId = document.getElementById('userId').value;
            
            try {
                const response = await fetch('/api/feedback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        user_id: userId,
                        item_id: itemId,
                        rating: rating,
                        type: 'rating'
                    })
                });
                
                const data = await response.json();
                
                if (data.status === 'success') {
                    alert('反馈提交成功！');
                } else {
                    alert('反馈提交失败: ' + data.message);
                }
            } catch (error) {
                alert('网络错误: ' + error.message);
            }
        }
        
        // 清空推荐结果
        function clearRecommendations() {
            document.getElementById('recommendations').innerHTML = '';
            currentRecommendations = [];
        }
        
        // 获取网络状态
        async function getNetworkStatus() {
            const networkInfoDiv = document.getElementById('networkInfo');
            networkInfoDiv.style.display = 'block';
            networkInfoDiv.innerHTML = '<div class="loading">正在获取网络状态...</div>';
            
            try {
                const response = await fetch('/api/network/status');
                const data = await response.json();
                
                if (data.status === 'success') {
                    const info = data.network_info;
                    networkInfoDiv.innerHTML = `
                        <h3>网络信息</h3>
                        <p><strong>本机IP:</strong> ${info.local_ip}</p>
                        <p><strong>主机名:</strong> ${info.hostname}</p>
                        <p><strong>操作系统:</strong> ${info.platform}</p>
                        <p><strong>网络段:</strong> ${info.network_segment || '未知'}</p>
                        <p><strong>已发现设备:</strong> ${data.network_info.discovered_devices || 0} 个</p>
                    `;
                } else {
                    networkInfoDiv.innerHTML = `<div class="error">获取网络状态失败: ${data.message}</div>`;
                }
            } catch (error) {
                networkInfoDiv.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 发现设备
        async function discoverDevices() {
            const deviceListDiv = document.getElementById('deviceList');
            deviceListDiv.innerHTML = '<div class="loading">正在扫描局域网设备...</div>';
            
            try {
                const response = await fetch('/api/network/discover');
                const data = await response.json();
                
                if (data.status === 'success') {
                    displayDevices(data.devices);
                } else {
                    deviceListDiv.innerHTML = `<div class="error">设备扫描失败: ${data.message}</div>`;
                }
            } catch (error) {
                deviceListDiv.innerHTML = `<div class="error">网络错误: ${error.message}</div>`;
            }
        }
        
        // 显示设备列表
        function displayDevices(devices) {
            const deviceListDiv = document.getElementById('deviceList');
            
            if (devices.length === 0) {
                deviceListDiv.innerHTML = '<div class="loading">未发现任何设备</div>';
                return;
            }
            
            let html = '<h3>发现的设备</h3>';
            devices.forEach(device => {
                const statusClass = device.status === 'online' ? 'status-online' : 'status-offline';
                const isLocal = device.is_local ? ' (本机)' : '';
                
                html += `
                    <div class="device-item">
                        <div>
                            <strong>${device.hostname || '未知设备'}${isLocal}</strong><br>
                            <small>IP: ${device.ip}</small>
                        </div>
                        <div class="device-status ${statusClass}">
                            ${device.status}
                        </div>
                    </div>
                `;
            });
            
            deviceListDiv.innerHTML = html;
        }
        
        // 页面加载完成后自动获取网络状态
        window.addEventListener('load', function() {
            getNetworkStatus();
        });
    </script>
</body>
</html>
