version: '3.8'

services:
  recommendation-system:
    build: .
    container_name: recommendation-agent
    ports:
      - "5000:5000"
    environment:
      - HOST=0.0.0.0
      - PORT=5000
      - DEBUG=false
      - NETWORK_SCAN_TIMEOUT=5
      - MAX_SCAN_THREADS=50
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    networks:
      - recommendation-net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: recommendation-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - recommendation-net
    restart: unless-stopped
    command: redis-server --appendonly yes

networks:
  recommendation-net:
    driver: bridge

volumes:
  redis_data:
