#!/usr/bin/env python3
"""
Docker部署脚本 - 智能推荐系统
支持一键构建、部署和管理Docker容器
"""

import os
import sys
import subprocess
import json
import time
import socket
from pathlib import Path

class DockerDeployer:
    """Docker部署管理器"""
    
    def __init__(self):
        self.project_name = "recommendation-system"
        self.image_name = "recommendation-agent"
        self.container_name = "recommendation-agent"
        self.port = 5000
        
    def check_docker(self):
        """检查Docker是否安装和运行"""
        try:
            # 检查Docker是否安装
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker未安装，请先安装Docker")
                return False
            
            print(f"✅ Docker已安装: {result.stdout.strip()}")
            
            # 检查Docker是否运行
            result = subprocess.run(['docker', 'info'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print("❌ Docker未运行，请启动Docker服务")
                return False
            
            print("✅ Docker服务正在运行")
            return True
            
        except FileNotFoundError:
            print("❌ Docker未安装，请先安装Docker")
            return False
    
    def check_docker_compose(self):
        """检查Docker Compose是否可用"""
        try:
            result = subprocess.run(['docker-compose', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Docker Compose已安装: {result.stdout.strip()}")
                return True
            else:
                # 尝试新版本的docker compose命令
                result = subprocess.run(['docker', 'compose', 'version'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"✅ Docker Compose已安装: {result.stdout.strip()}")
                    return True
                else:
                    print("⚠️  Docker Compose未安装，将使用基础Docker命令")
                    return False
        except FileNotFoundError:
            print("⚠️  Docker Compose未安装，将使用基础Docker命令")
            return False
    
    def build_image(self):
        """构建Docker镜像"""
        print("🔨 开始构建Docker镜像...")
        
        try:
            cmd = ['docker', 'build', '-t', self.image_name, '.']
            result = subprocess.run(cmd, check=True)
            
            print("✅ Docker镜像构建成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 镜像构建失败: {e}")
            return False
    
    def run_container(self):
        """运行Docker容器"""
        print("🚀 启动Docker容器...")
        
        try:
            # 停止并删除现有容器
            self.stop_container()
            
            # 获取本机IP
            local_ip = self.get_local_ip()
            
            # 运行新容器
            cmd = [
                'docker', 'run', '-d',
                '--name', self.container_name,
                '-p', f'{self.port}:{self.port}',
                '-v', f'{os.getcwd()}/data:/app/data',
                '-v', f'{os.getcwd()}/logs:/app/logs',
                '--restart', 'unless-stopped',
                self.image_name
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            container_id = result.stdout.strip()
            
            print(f"✅ 容器启动成功: {container_id[:12]}")
            print(f"🌐 访问地址:")
            print(f"   本地: http://localhost:{self.port}")
            print(f"   局域网: http://{local_ip}:{self.port}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 容器启动失败: {e}")
            return False
    
    def run_with_compose(self):
        """使用Docker Compose运行"""
        print("🚀 使用Docker Compose启动服务...")
        
        try:
            # 检查是否使用新版本的docker compose
            try:
                subprocess.run(['docker', 'compose', 'version'], 
                             capture_output=True, check=True)
                compose_cmd = ['docker', 'compose']
            except:
                compose_cmd = ['docker-compose']
            
            # 停止现有服务
            subprocess.run(compose_cmd + ['down'], capture_output=True)
            
            # 启动服务
            cmd = compose_cmd + ['up', '-d', '--build']
            result = subprocess.run(cmd, check=True)
            
            # 获取本机IP
            local_ip = self.get_local_ip()
            
            print("✅ 服务启动成功")
            print(f"🌐 访问地址:")
            print(f"   本地: http://localhost:{self.port}")
            print(f"   局域网: http://{local_ip}:{self.port}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 服务启动失败: {e}")
            return False
    
    def stop_container(self):
        """停止容器"""
        try:
            # 停止容器
            subprocess.run(['docker', 'stop', self.container_name], 
                         capture_output=True)
            # 删除容器
            subprocess.run(['docker', 'rm', self.container_name], 
                         capture_output=True)
        except:
            pass
    
    def stop_compose(self):
        """停止Docker Compose服务"""
        try:
            # 检查是否使用新版本的docker compose
            try:
                subprocess.run(['docker', 'compose', 'version'], 
                             capture_output=True, check=True)
                compose_cmd = ['docker', 'compose']
            except:
                compose_cmd = ['docker-compose']
            
            subprocess.run(compose_cmd + ['down'], check=True)
            print("✅ 服务已停止")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 停止服务失败: {e}")
    
    def get_status(self):
        """获取容器状态"""
        try:
            result = subprocess.run(['docker', 'ps', '--filter', f'name={self.container_name}', 
                                   '--format', 'table {{.Names}}\t{{.Status}}\t{{.Ports}}'], 
                                  capture_output=True, text=True)
            
            if result.stdout.strip():
                print("📊 容器状态:")
                print(result.stdout)
            else:
                print("📊 容器未运行")
                
        except subprocess.CalledProcessError as e:
            print(f"❌ 获取状态失败: {e}")
    
    def view_logs(self):
        """查看容器日志"""
        try:
            print("📋 容器日志 (按Ctrl+C退出):")
            subprocess.run(['docker', 'logs', '-f', self.container_name])
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 查看日志失败: {e}")
        except KeyboardInterrupt:
            print("\n日志查看已退出")
    
    def get_local_ip(self):
        """获取本机IP地址"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                return s.getsockname()[0]
        except Exception:
            return "127.0.0.1"
    
    def create_directories(self):
        """创建必要的目录"""
        directories = ['data', 'logs']
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
        print("✅ 目录结构已创建")
    
    def export_image(self, filename=None):
        """导出Docker镜像"""
        if not filename:
            filename = f"{self.image_name}.tar"
        
        print(f"📦 导出镜像到 {filename}...")
        
        try:
            cmd = ['docker', 'save', '-o', filename, self.image_name]
            subprocess.run(cmd, check=True)
            
            # 获取文件大小
            size = os.path.getsize(filename) / (1024 * 1024)
            print(f"✅ 镜像导出成功: {filename} ({size:.1f} MB)")
            print(f"💡 可以使用以下命令在其他机器上加载镜像:")
            print(f"   docker load -i {filename}")
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 镜像导出失败: {e}")
    
    def load_image(self, filename):
        """加载Docker镜像"""
        if not os.path.exists(filename):
            print(f"❌ 文件不存在: {filename}")
            return False
        
        print(f"📥 加载镜像从 {filename}...")
        
        try:
            cmd = ['docker', 'load', '-i', filename]
            subprocess.run(cmd, check=True)
            print("✅ 镜像加载成功")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 镜像加载失败: {e}")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="智能推荐系统 Docker 部署工具")
    parser.add_argument('action', choices=[
        'build', 'run', 'stop', 'status', 'logs', 
        'compose-up', 'compose-down', 'export', 'load'
    ], help='执行的操作')
    parser.add_argument('--file', help='镜像文件路径 (用于export/load操作)')
    
    args = parser.parse_args()
    
    deployer = DockerDeployer()
    
    print("🐳 智能推荐系统 Docker 部署工具")
    print("=" * 50)
    
    # 检查Docker环境
    if not deployer.check_docker():
        sys.exit(1)
    
    # 创建目录
    deployer.create_directories()
    
    # 执行操作
    if args.action == 'build':
        deployer.build_image()
        
    elif args.action == 'run':
        if not deployer.build_image():
            sys.exit(1)
        deployer.run_container()
        
    elif args.action == 'stop':
        deployer.stop_container()
        print("✅ 容器已停止")
        
    elif args.action == 'status':
        deployer.get_status()
        
    elif args.action == 'logs':
        deployer.view_logs()
        
    elif args.action == 'compose-up':
        if deployer.check_docker_compose():
            deployer.run_with_compose()
        else:
            print("❌ Docker Compose不可用")
            
    elif args.action == 'compose-down':
        if deployer.check_docker_compose():
            deployer.stop_compose()
        else:
            print("❌ Docker Compose不可用")
            
    elif args.action == 'export':
        filename = args.file or f"{deployer.image_name}.tar"
        deployer.export_image(filename)
        
    elif args.action == 'load':
        if not args.file:
            print("❌ 请指定镜像文件: --file <filename>")
            sys.exit(1)
        deployer.load_image(args.file)

if __name__ == "__main__":
    main()
