import socket
import subprocess
import platform
import ipaddress
import threading
import time
import json
import logging
from typing import List, Dict, Any
import requests
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

logger = logging.getLogger(__name__)

class NetworkManager:
    """网络管理器 - 用于局域网搭建和设备管理"""
    
    def __init__(self):
        self.local_ip = self._get_local_ip()
        self.network_info = self._get_network_info()
        self.discovered_devices = []
        self.services = {}
        
    def _get_local_ip(self) -> str:
        """获取本机IP地址"""
        try:
            # 连接到外部地址来获取本机IP
            with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
                s.connect(("*******", 80))
                local_ip = s.getsockname()[0]
            return local_ip
        except Exception as e:
            logger.error(f"获取本机IP失败: {str(e)}")
            return "127.0.0.1"
    
    def _get_network_info(self) -> Dict[str, Any]:
        """获取网络信息"""
        try:
            # 获取网络接口信息
            if platform.system() == "Windows":
                result = subprocess.run(["ipconfig"], capture_output=True, text=True, encoding='gbk')
            else:
                result = subprocess.run(["ifconfig"], capture_output=True, text=True)
            
            # 获取网关信息
            if platform.system() == "Windows":
                gateway_result = subprocess.run(["route", "print", "0.0.0.0"], capture_output=True, text=True, encoding='gbk')
            else:
                gateway_result = subprocess.run(["route", "-n"], capture_output=True, text=True)
            
            # 解析网络信息
            network_info = {
                "local_ip": self.local_ip,
                "platform": platform.system(),
                "hostname": socket.gethostname(),
                "network_interfaces": result.stdout if result.returncode == 0 else "获取失败",
                "gateway_info": gateway_result.stdout if gateway_result.returncode == 0 else "获取失败"
            }
            
            # 计算网络段
            try:
                network = ipaddress.IPv4Network(f"{self.local_ip}/24", strict=False)
                network_info["network_segment"] = str(network)
                network_info["network_range"] = f"{network.network_address} - {network.broadcast_address}"
            except Exception as e:
                logger.error(f"计算网络段失败: {str(e)}")
                network_info["network_segment"] = "未知"
                network_info["network_range"] = "未知"
            
            return network_info
            
        except Exception as e:
            logger.error(f"获取网络信息失败: {str(e)}")
            return {
                "local_ip": self.local_ip,
                "platform": platform.system(),
                "hostname": socket.gethostname(),
                "error": str(e)
            }
    
    def get_status(self) -> Dict[str, Any]:
        """获取网络状态"""
        return {
            "network_info": self.network_info,
            "discovered_devices": len(self.discovered_devices),
            "active_services": list(self.services.keys()),
            "last_scan": getattr(self, 'last_scan_time', None)
        }
    
    def discover_devices(self, timeout: int = 5) -> List[Dict[str, Any]]:
        """发现局域网设备"""
        try:
            logger.info("开始扫描局域网设备...")
            self.discovered_devices = []
            
            # 获取网络段
            try:
                network = ipaddress.IPv4Network(f"{self.local_ip}/24", strict=False)
            except Exception as e:
                logger.error(f"无法确定网络段: {str(e)}")
                return []
            
            # 多线程扫描
            with ThreadPoolExecutor(max_workers=50) as executor:
                futures = []
                
                for ip in network.hosts():
                    if str(ip) != self.local_ip:  # 跳过本机
                        future = executor.submit(self._scan_host, str(ip), timeout)
                        futures.append(future)
                
                # 收集结果
                for future in as_completed(futures, timeout=timeout + 10):
                    try:
                        result = future.result()
                        if result:
                            self.discovered_devices.append(result)
                    except Exception as e:
                        logger.debug(f"扫描主机时出错: {str(e)}")
            
            # 添加本机信息
            self.discovered_devices.append({
                "ip": self.local_ip,
                "hostname": socket.gethostname(),
                "status": "online",
                "is_local": True,
                "services": self._scan_local_services(),
                "scan_time": time.time()
            })
            
            self.last_scan_time = time.time()
            logger.info(f"扫描完成，发现 {len(self.discovered_devices)} 个设备")
            
            return self.discovered_devices
            
        except Exception as e:
            logger.error(f"设备发现失败: {str(e)}")
            return []
    
    def _scan_host(self, ip: str, timeout: int) -> Dict[str, Any]:
        """扫描单个主机"""
        try:
            # Ping测试
            if platform.system() == "Windows":
                ping_cmd = ["ping", "-n", "1", "-w", str(timeout * 1000), ip]
            else:
                ping_cmd = ["ping", "-c", "1", "-W", str(timeout), ip]
            
            result = subprocess.run(ping_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                # 主机在线，获取更多信息
                device_info = {
                    "ip": ip,
                    "status": "online",
                    "is_local": False,
                    "scan_time": time.time()
                }
                
                # 尝试获取主机名
                try:
                    hostname = socket.gethostbyaddr(ip)[0]
                    device_info["hostname"] = hostname
                except:
                    device_info["hostname"] = "未知"
                
                # 扫描常见端口
                device_info["services"] = self._scan_ports(ip, [22, 23, 53, 80, 135, 139, 443, 445, 993, 995])
                
                return device_info
            
        except Exception as e:
            logger.debug(f"扫描主机 {ip} 失败: {str(e)}")
        
        return None
    
    def _scan_ports(self, ip: str, ports: List[int], timeout: float = 1.0) -> List[Dict[str, Any]]:
        """扫描主机端口"""
        open_ports = []
        
        for port in ports:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(timeout)
                    result = sock.connect_ex((ip, port))
                    
                    if result == 0:
                        service_name = self._get_service_name(port)
                        open_ports.append({
                            "port": port,
                            "service": service_name,
                            "status": "open"
                        })
            except Exception as e:
                logger.debug(f"扫描端口 {ip}:{port} 失败: {str(e)}")
        
        return open_ports
    
    def _scan_local_services(self) -> List[Dict[str, Any]]:
        """扫描本机服务"""
        services = []
        
        # 检查常见服务端口
        common_ports = [22, 23, 53, 80, 135, 139, 443, 445, 993, 995, 3389, 5000, 8080, 8443]
        
        for port in common_ports:
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(1.0)
                    result = sock.connect_ex(("127.0.0.1", port))
                    
                    if result == 0:
                        service_name = self._get_service_name(port)
                        services.append({
                            "port": port,
                            "service": service_name,
                            "status": "running"
                        })
            except Exception as e:
                logger.debug(f"检查本地端口 {port} 失败: {str(e)}")
        
        return services
    
    def _get_service_name(self, port: int) -> str:
        """根据端口号获取服务名称"""
        service_map = {
            22: "SSH",
            23: "Telnet", 
            53: "DNS",
            80: "HTTP",
            135: "RPC",
            139: "NetBIOS",
            443: "HTTPS",
            445: "SMB",
            993: "IMAPS",
            995: "POP3S",
            3389: "RDP",
            5000: "Flask/HTTP",
            8080: "HTTP-Alt",
            8443: "HTTPS-Alt"
        }
        
        return service_map.get(port, f"Unknown-{port}")
    
    def start_service(self, service_name: str, port: int, handler=None) -> bool:
        """启动网络服务"""
        try:
            if service_name in self.services:
                logger.warning(f"服务 {service_name} 已在运行")
                return False
            
            # 检查端口是否可用
            if not self._is_port_available(port):
                logger.error(f"端口 {port} 不可用")
                return False
            
            # 启动服务
            service_info = {
                "name": service_name,
                "port": port,
                "status": "running",
                "start_time": time.time()
            }
            
            self.services[service_name] = service_info
            logger.info(f"服务 {service_name} 在端口 {port} 启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动服务失败: {str(e)}")
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止网络服务"""
        try:
            if service_name not in self.services:
                logger.warning(f"服务 {service_name} 未运行")
                return False
            
            del self.services[service_name]
            logger.info(f"服务 {service_name} 已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止服务失败: {str(e)}")
            return False
    
    def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.bind(("", port))
                return True
        except OSError:
            return False
    
    def get_network_config(self) -> Dict[str, Any]:
        """获取网络配置建议"""
        config = {
            "recommended_settings": {
                "host": "0.0.0.0",  # 监听所有接口
                "port": 5000,
                "debug": False,
                "threaded": True
            },
            "firewall_rules": [
                f"允许入站连接到端口 5000",
                f"允许来自 {self.network_info.get('network_segment', '局域网')} 的连接"
            ],
            "access_urls": [
                f"http://{self.local_ip}:5000",
                f"http://localhost:5000"
            ],
            "security_notes": [
                "确保防火墙允许相应端口",
                "在生产环境中禁用调试模式",
                "考虑使用HTTPS加密连接",
                "定期更新依赖包"
            ]
        }
        
        return config
