import numpy as np
import pandas as pd
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import TruncatedSVD
import json
import os
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional
import pickle

logger = logging.getLogger(__name__)

class RecommendationAgent:
    """智能推荐代理 - 基于机器学习的推荐系统"""
    
    def __init__(self, data_path: str = "data"):
        self.data_path = data_path
        self.user_profiles = {}
        self.item_features = {}
        self.interaction_history = []
        self.model = None
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.svd_model = TruncatedSVD(n_components=50)
        
        # 确保数据目录存在
        os.makedirs(data_path, exist_ok=True)
        
        # 加载现有数据
        self._load_data()
        
        # 初始化示例数据
        self._initialize_sample_data()
    
    def _load_data(self):
        """加载保存的数据"""
        try:
            # 加载用户档案
            user_file = os.path.join(self.data_path, "user_profiles.json")
            if os.path.exists(user_file):
                with open(user_file, 'r', encoding='utf-8') as f:
                    self.user_profiles = json.load(f)
            
            # 加载物品特征
            item_file = os.path.join(self.data_path, "item_features.json")
            if os.path.exists(item_file):
                with open(item_file, 'r', encoding='utf-8') as f:
                    self.item_features = json.load(f)
            
            # 加载交互历史
            history_file = os.path.join(self.data_path, "interaction_history.json")
            if os.path.exists(history_file):
                with open(history_file, 'r', encoding='utf-8') as f:
                    self.interaction_history = json.load(f)
                    
            logger.info("数据加载完成")
            
        except Exception as e:
            logger.error(f"加载数据时出错: {str(e)}")
    
    def _save_data(self):
        """保存数据"""
        try:
            # 保存用户档案
            with open(os.path.join(self.data_path, "user_profiles.json"), 'w', encoding='utf-8') as f:
                json.dump(self.user_profiles, f, ensure_ascii=False, indent=2)
            
            # 保存物品特征
            with open(os.path.join(self.data_path, "item_features.json"), 'w', encoding='utf-8') as f:
                json.dump(self.item_features, f, ensure_ascii=False, indent=2)
            
            # 保存交互历史
            with open(os.path.join(self.data_path, "interaction_history.json"), 'w', encoding='utf-8') as f:
                json.dump(self.interaction_history, f, ensure_ascii=False, indent=2)
                
            logger.info("数据保存完成")
            
        except Exception as e:
            logger.error(f"保存数据时出错: {str(e)}")
    
    def _initialize_sample_data(self):
        """初始化示例数据"""
        if not self.item_features:
            # 示例物品数据
            sample_items = [
                {
                    "id": "item_1",
                    "title": "Python机器学习实战",
                    "category": "编程书籍",
                    "description": "深入学习Python机器学习算法和实践",
                    "tags": ["python", "机器学习", "编程", "算法"],
                    "rating": 4.5,
                    "price": 89.0
                },
                {
                    "id": "item_2", 
                    "title": "深度学习框架TensorFlow",
                    "category": "编程书籍",
                    "description": "TensorFlow深度学习框架详解",
                    "tags": ["tensorflow", "深度学习", "神经网络", "AI"],
                    "rating": 4.3,
                    "price": 95.0
                },
                {
                    "id": "item_3",
                    "title": "数据科学入门",
                    "category": "数据科学",
                    "description": "数据科学基础知识和实践指南",
                    "tags": ["数据科学", "统计学", "分析", "可视化"],
                    "rating": 4.2,
                    "price": 78.0
                },
                {
                    "id": "item_4",
                    "title": "Web开发全栈指南",
                    "category": "Web开发",
                    "description": "前端后端全栈开发技术详解",
                    "tags": ["web开发", "前端", "后端", "全栈"],
                    "rating": 4.4,
                    "price": 108.0
                },
                {
                    "id": "item_5",
                    "title": "云计算与DevOps",
                    "category": "云计算",
                    "description": "云计算平台和DevOps实践",
                    "tags": ["云计算", "devops", "容器", "微服务"],
                    "rating": 4.1,
                    "price": 125.0
                }
            ]
            
            for item in sample_items:
                self.item_features[item["id"]] = item
            
            self._save_data()
    
    def get_recommendations(self, user_id: str, query: str = "", preferences: Dict = None, num_recommendations: int = 5) -> List[Dict]:
        """获取推荐结果"""
        try:
            # 获取或创建用户档案
            user_profile = self._get_user_profile(user_id)
            
            # 基于内容的推荐
            content_recommendations = self._content_based_recommendations(user_profile, query, preferences)
            
            # 基于协同过滤的推荐
            collaborative_recommendations = self._collaborative_filtering_recommendations(user_id)
            
            # 混合推荐策略
            final_recommendations = self._hybrid_recommendations(
                content_recommendations, 
                collaborative_recommendations, 
                num_recommendations
            )
            
            # 记录推荐日志
            self._log_recommendation(user_id, query, final_recommendations)
            
            return final_recommendations
            
        except Exception as e:
            logger.error(f"获取推荐时出错: {str(e)}")
            return []
    
    def _get_user_profile(self, user_id: str) -> Dict:
        """获取或创建用户档案"""
        if user_id not in self.user_profiles:
            self.user_profiles[user_id] = {
                "id": user_id,
                "preferences": {},
                "interaction_count": 0,
                "favorite_categories": [],
                "average_rating": 0.0,
                "created_at": datetime.now().isoformat()
            }
        return self.user_profiles[user_id]
    
    def _content_based_recommendations(self, user_profile: Dict, query: str, preferences: Dict) -> List[Dict]:
        """基于内容的推荐"""
        recommendations = []
        
        # 构建物品文本特征
        item_texts = []
        item_ids = []
        
        for item_id, item in self.item_features.items():
            text = f"{item['title']} {item['description']} {' '.join(item['tags'])}"
            item_texts.append(text)
            item_ids.append(item_id)
        
        if not item_texts:
            return recommendations
        
        # TF-IDF向量化
        tfidf_matrix = self.vectorizer.fit_transform(item_texts)
        
        # 如果有查询，计算查询相似度
        if query:
            query_vector = self.vectorizer.transform([query])
            similarities = cosine_similarity(query_vector, tfidf_matrix).flatten()
        else:
            # 基于用户偏好计算相似度
            user_pref_text = " ".join(user_profile.get("favorite_categories", []))
            if user_pref_text:
                pref_vector = self.vectorizer.transform([user_pref_text])
                similarities = cosine_similarity(pref_vector, tfidf_matrix).flatten()
            else:
                similarities = np.random.random(len(item_ids))  # 随机推荐
        
        # 排序并获取推荐
        sorted_indices = np.argsort(similarities)[::-1]
        
        for idx in sorted_indices[:10]:  # 取前10个
            item_id = item_ids[idx]
            item = self.item_features[item_id]
            recommendations.append({
                "item_id": item_id,
                "title": item["title"],
                "category": item["category"],
                "description": item["description"],
                "rating": item["rating"],
                "price": item["price"],
                "similarity_score": float(similarities[idx]),
                "recommendation_type": "content_based"
            })
        
        return recommendations
    
    def _collaborative_filtering_recommendations(self, user_id: str) -> List[Dict]:
        """基于协同过滤的推荐"""
        # 简化的协同过滤实现
        recommendations = []
        
        # 基于用户交互历史找相似用户
        user_interactions = [h for h in self.interaction_history if h["user_id"] == user_id]
        
        if not user_interactions:
            return recommendations
        
        # 找到相似用户喜欢的物品
        user_items = set([h["item_id"] for h in user_interactions if h["rating"] >= 4])
        
        for item_id, item in self.item_features.items():
            if item_id not in user_items:
                recommendations.append({
                    "item_id": item_id,
                    "title": item["title"],
                    "category": item["category"],
                    "description": item["description"],
                    "rating": item["rating"],
                    "price": item["price"],
                    "similarity_score": item["rating"] / 5.0,
                    "recommendation_type": "collaborative"
                })
        
        return sorted(recommendations, key=lambda x: x["similarity_score"], reverse=True)[:5]
    
    def _hybrid_recommendations(self, content_recs: List[Dict], collab_recs: List[Dict], num_recommendations: int) -> List[Dict]:
        """混合推荐策略"""
        # 简单的混合策略：内容推荐权重0.7，协同过滤权重0.3
        all_recommendations = {}
        
        # 处理内容推荐
        for rec in content_recs:
            item_id = rec["item_id"]
            if item_id not in all_recommendations:
                all_recommendations[item_id] = rec.copy()
                all_recommendations[item_id]["final_score"] = rec["similarity_score"] * 0.7
            else:
                all_recommendations[item_id]["final_score"] += rec["similarity_score"] * 0.7
        
        # 处理协同过滤推荐
        for rec in collab_recs:
            item_id = rec["item_id"]
            if item_id not in all_recommendations:
                all_recommendations[item_id] = rec.copy()
                all_recommendations[item_id]["final_score"] = rec["similarity_score"] * 0.3
            else:
                all_recommendations[item_id]["final_score"] += rec["similarity_score"] * 0.3
        
        # 排序并返回
        final_recs = list(all_recommendations.values())
        final_recs.sort(key=lambda x: x["final_score"], reverse=True)
        
        return final_recs[:num_recommendations]
    
    def process_feedback(self, user_id: str, item_id: str, rating: float, feedback_type: str = "rating") -> bool:
        """处理用户反馈"""
        try:
            # 记录交互历史
            interaction = {
                "user_id": user_id,
                "item_id": item_id,
                "rating": rating,
                "feedback_type": feedback_type,
                "timestamp": datetime.now().isoformat()
            }
            
            self.interaction_history.append(interaction)
            
            # 更新用户档案
            user_profile = self._get_user_profile(user_id)
            user_profile["interaction_count"] += 1
            
            # 更新用户偏好
            if item_id in self.item_features:
                item = self.item_features[item_id]
                category = item["category"]
                
                if category not in user_profile["favorite_categories"] and rating >= 4:
                    user_profile["favorite_categories"].append(category)
            
            # 保存数据
            self._save_data()
            
            logger.info(f"用户 {user_id} 对物品 {item_id} 的反馈已处理")
            return True
            
        except Exception as e:
            logger.error(f"处理反馈时出错: {str(e)}")
            return False
    
    def _log_recommendation(self, user_id: str, query: str, recommendations: List[Dict]):
        """记录推荐日志"""
        log_entry = {
            "user_id": user_id,
            "query": query,
            "recommendations": [r["item_id"] for r in recommendations],
            "timestamp": datetime.now().isoformat()
        }
        
        # 可以保存到日志文件或数据库
        logger.info(f"为用户 {user_id} 生成了 {len(recommendations)} 个推荐")
