#!/usr/bin/env python3
"""
智能推荐系统一键安装脚本
支持多种安装方式：本地安装、Docker安装、镜像安装
"""

import os
import sys
import subprocess
import platform
import urllib.request
import json
from pathlib import Path

class RecommendationInstaller:
    """推荐系统安装器"""
    
    def __init__(self):
        self.system = platform.system()
        self.python_version = sys.version_info
        self.project_dir = Path.cwd()
        
    def check_requirements(self):
        """检查系统要求"""
        print("🔍 检查系统要求...")
        
        # 检查Python版本
        if self.python_version < (3, 7):
            print(f"❌ Python版本过低: {sys.version}")
            print("   需要Python 3.7或更高版本")
            return False
        
        print(f"✅ Python版本: {sys.version}")
        
        # 检查pip
        try:
            import pip
            print("✅ pip已安装")
        except ImportError:
            print("❌ pip未安装")
            return False
        
        return True
    
    def install_local(self):
        """本地安装"""
        print("\n📦 开始本地安装...")
        
        try:
            # 安装依赖
            print("正在安装Python依赖包...")
            subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                         check=True)
            
            # 创建目录
            self.create_directories()
            
            # 设置环境变量
            self.setup_environment()
            
            print("✅ 本地安装完成!")
            print("\n🚀 启动命令:")
            print("   python start.py")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 安装失败: {e}")
            return False
    
    def install_docker(self):
        """Docker安装"""
        print("\n🐳 开始Docker安装...")
        
        # 检查Docker
        if not self.check_docker():
            print("❌ Docker不可用，请先安装Docker")
            self.show_docker_install_guide()
            return False
        
        try:
            # 构建镜像
            print("正在构建Docker镜像...")
            subprocess.run(['python', 'docker_deploy.py', 'build'], check=True)
            
            # 运行容器
            print("正在启动容器...")
            subprocess.run(['python', 'docker_deploy.py', 'run'], check=True)
            
            print("✅ Docker安装完成!")
            print("\n🚀 管理命令:")
            print("   查看状态: python docker_deploy.py status")
            print("   查看日志: python docker_deploy.py logs")
            print("   停止服务: python docker_deploy.py stop")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Docker安装失败: {e}")
            return False
    
    def install_from_image(self, image_file=None):
        """从镜像文件安装"""
        print("\n💿 从镜像文件安装...")
        
        if not image_file:
            # 查找镜像文件
            image_files = list(Path('.').glob('*.tar'))
            if not image_files:
                print("❌ 未找到镜像文件 (*.tar)")
                print("   请将镜像文件放在当前目录")
                return False
            
            image_file = image_files[0]
            print(f"📦 找到镜像文件: {image_file}")
        
        # 检查Docker
        if not self.check_docker():
            print("❌ Docker不可用，请先安装Docker")
            return False
        
        try:
            # 加载镜像
            print("正在加载Docker镜像...")
            subprocess.run(['python', 'docker_deploy.py', 'load', '--file', str(image_file)], 
                         check=True)
            
            # 运行容器
            print("正在启动容器...")
            subprocess.run(['python', 'docker_deploy.py', 'run'], check=True)
            
            print("✅ 镜像安装完成!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 镜像安装失败: {e}")
            return False
    
    def check_docker(self):
        """检查Docker是否可用"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Docker已安装: {result.stdout.strip()}")
                
                # 检查Docker是否运行
                result = subprocess.run(['docker', 'info'], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    print("✅ Docker服务正在运行")
                    return True
                else:
                    print("❌ Docker服务未运行")
                    return False
            else:
                return False
        except FileNotFoundError:
            return False
    
    def show_docker_install_guide(self):
        """显示Docker安装指南"""
        print("\n📖 Docker安装指南:")
        print("=" * 50)
        
        if self.system == "Windows":
            print("Windows:")
            print("1. 下载Docker Desktop: https://www.docker.com/products/docker-desktop")
            print("2. 运行安装程序并重启电脑")
            print("3. 启动Docker Desktop")
            
        elif self.system == "Darwin":  # macOS
            print("macOS:")
            print("1. 下载Docker Desktop: https://www.docker.com/products/docker-desktop")
            print("2. 拖拽到Applications文件夹")
            print("3. 启动Docker Desktop")
            
        else:  # Linux
            print("Linux (Ubuntu/Debian):")
            print("sudo apt update")
            print("sudo apt install docker.io")
            print("sudo systemctl start docker")
            print("sudo systemctl enable docker")
            print("sudo usermod -aG docker $USER")
            print("(重新登录以应用用户组更改)")
    
    def create_directories(self):
        """创建必要的目录"""
        directories = ['data', 'logs', 'templates']
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
        print("✅ 目录结构已创建")
    
    def setup_environment(self):
        """设置环境变量"""
        env_file = Path('.env')
        if not env_file.exists():
            print("✅ 环境配置文件已存在")
        else:
            print("✅ 使用现有环境配置")
    
    def show_menu(self):
        """显示安装菜单"""
        print("\n" + "=" * 60)
        print("🤖 智能推荐系统安装向导")
        print("=" * 60)
        print("请选择安装方式:")
        print()
        print("1. 📦 本地安装 (推荐用于开发)")
        print("   - 直接在本机安装Python依赖")
        print("   - 适合开发和测试")
        print()
        print("2. 🐳 Docker安装 (推荐用于生产)")
        print("   - 使用Docker容器运行")
        print("   - 环境隔离，易于部署")
        print()
        print("3. 💿 镜像安装 (用于离线部署)")
        print("   - 从预构建的镜像文件安装")
        print("   - 适合无网络环境")
        print()
        print("4. ❓ 查看系统信息")
        print("5. 🚪 退出")
        print("=" * 60)
    
    def show_system_info(self):
        """显示系统信息"""
        print("\n💻 系统信息:")
        print("=" * 40)
        print(f"操作系统: {platform.system()} {platform.release()}")
        print(f"Python版本: {sys.version}")
        print(f"工作目录: {self.project_dir}")
        
        # 检查Docker
        docker_status = "可用" if self.check_docker() else "不可用"
        print(f"Docker状态: {docker_status}")
        
        # 检查文件
        required_files = ['app.py', 'requirements.txt', 'Dockerfile']
        print("\n📁 项目文件:")
        for file in required_files:
            status = "✅" if Path(file).exists() else "❌"
            print(f"   {status} {file}")
    
    def run(self):
        """运行安装向导"""
        print("🎯 智能推荐系统 - 一键安装工具")
        
        # 检查系统要求
        if not self.check_requirements():
            print("\n❌ 系统要求检查失败，请解决上述问题后重试")
            return False
        
        while True:
            self.show_menu()
            
            try:
                choice = input("\n请输入选择 (1-5): ").strip()
                
                if choice == '1':
                    success = self.install_local()
                    if success:
                        break
                        
                elif choice == '2':
                    success = self.install_docker()
                    if success:
                        break
                        
                elif choice == '3':
                    success = self.install_from_image()
                    if success:
                        break
                        
                elif choice == '4':
                    self.show_system_info()
                    input("\n按回车键继续...")
                    
                elif choice == '5':
                    print("👋 安装已取消")
                    return False
                    
                else:
                    print("❌ 无效选择，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n\n👋 安装已取消")
                return False
        
        print("\n🎉 安装完成！")
        print("📖 更多信息请查看 README.md")
        return True

def main():
    """主函数"""
    installer = RecommendationInstaller()
    installer.run()

if __name__ == "__main__":
    main()
