from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import os
from dotenv import load_dotenv
from recommendation_agent import RecommendationAgent
from network_manager import NetworkManager
import logging

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize components
recommendation_agent = RecommendationAgent()
network_manager = NetworkManager()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/recommend', methods=['POST'])
def get_recommendations():
    """Get recommendations based on user input"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        query = data.get('query', '')
        preferences = data.get('preferences', {})
        
        # Get recommendations from the agent
        recommendations = recommendation_agent.get_recommendations(
            user_id=user_id,
            query=query,
            preferences=preferences
        )
        
        return jsonify({
            'status': 'success',
            'recommendations': recommendations
        })
    
    except Exception as e:
        logger.error(f"Error getting recommendations: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/feedback', methods=['POST'])
def submit_feedback():
    """Submit user feedback for recommendations"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        item_id = data.get('item_id')
        rating = data.get('rating')
        feedback_type = data.get('type', 'rating')
        
        # Process feedback
        result = recommendation_agent.process_feedback(
            user_id=user_id,
            item_id=item_id,
            rating=rating,
            feedback_type=feedback_type
        )
        
        return jsonify({
            'status': 'success',
            'message': 'Feedback processed successfully'
        })
    
    except Exception as e:
        logger.error(f"Error processing feedback: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/network/status', methods=['GET'])
def network_status():
    """Get network status and configuration"""
    try:
        status = network_manager.get_status()
        return jsonify({
            'status': 'success',
            'network_info': status
        })
    
    except Exception as e:
        logger.error(f"Error getting network status: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/api/network/discover', methods=['GET'])
def discover_devices():
    """Discover devices on local network"""
    try:
        devices = network_manager.discover_devices()
        return jsonify({
            'status': 'success',
            'devices': devices
        })
    
    except Exception as e:
        logger.error(f"Error discovering devices: {str(e)}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'recommendation-agent',
        'version': '1.0.0'
    })

if __name__ == '__main__':
    # Get configuration from environment
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('DEBUG', 'False').lower() == 'true'
    
    logger.info(f"Starting recommendation agent on {host}:{port}")
    app.run(host=host, port=port, debug=debug)
