#!/usr/bin/env python3
"""
智能推荐系统测试脚本
用于验证系统各项功能是否正常工作
"""

import requests
import json
import time
import sys
from typing import Dict, Any

class SystemTester:
    """系统测试类"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def test_health_check(self) -> bool:
        """测试健康检查接口"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'healthy':
                    self.log_success("健康检查", "服务正常运行")
                    return True
            
            self.log_error("健康检查", f"状态码: {response.status_code}")
            return False
            
        except Exception as e:
            self.log_error("健康检查", f"连接失败: {str(e)}")
            return False
    
    def test_recommendations(self) -> bool:
        """测试推荐接口"""
        try:
            test_data = {
                "user_id": "test_user_001",
                "query": "机器学习",
                "preferences": {
                    "category": "编程书籍"
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/recommend",
                json=test_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    recommendations = data.get('recommendations', [])
                    self.log_success("推荐接口", f"获得 {len(recommendations)} 个推荐")
                    
                    # 显示推荐结果
                    for i, rec in enumerate(recommendations[:3], 1):
                        print(f"    {i}. {rec.get('title', '未知')} - {rec.get('category', '未知')}")
                    
                    return True
                else:
                    self.log_error("推荐接口", data.get('message', '未知错误'))
                    return False
            else:
                self.log_error("推荐接口", f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_error("推荐接口", f"请求失败: {str(e)}")
            return False
    
    def test_feedback(self) -> bool:
        """测试反馈接口"""
        try:
            test_data = {
                "user_id": "test_user_001",
                "item_id": "item_1",
                "rating": 5,
                "type": "rating"
            }
            
            response = self.session.post(
                f"{self.base_url}/api/feedback",
                json=test_data,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    self.log_success("反馈接口", "反馈提交成功")
                    return True
                else:
                    self.log_error("反馈接口", data.get('message', '未知错误'))
                    return False
            else:
                self.log_error("反馈接口", f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_error("反馈接口", f"请求失败: {str(e)}")
            return False
    
    def test_network_status(self) -> bool:
        """测试网络状态接口"""
        try:
            response = self.session.get(f"{self.base_url}/api/network/status", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    network_info = data.get('network_info', {})
                    local_ip = network_info.get('local_ip', '未知')
                    hostname = network_info.get('hostname', '未知')
                    
                    self.log_success("网络状态", f"IP: {local_ip}, 主机: {hostname}")
                    return True
                else:
                    self.log_error("网络状态", data.get('message', '未知错误'))
                    return False
            else:
                self.log_error("网络状态", f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_error("网络状态", f"请求失败: {str(e)}")
            return False
    
    def test_device_discovery(self) -> bool:
        """测试设备发现接口"""
        try:
            print("    正在扫描局域网设备 (可能需要几秒钟)...")
            
            response = self.session.get(f"{self.base_url}/api/network/discover", timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('status') == 'success':
                    devices = data.get('devices', [])
                    self.log_success("设备发现", f"发现 {len(devices)} 个设备")
                    
                    # 显示发现的设备
                    for device in devices[:5]:  # 只显示前5个
                        ip = device.get('ip', '未知')
                        hostname = device.get('hostname', '未知')
                        is_local = " (本机)" if device.get('is_local') else ""
                        print(f"    - {hostname}{is_local}: {ip}")
                    
                    return True
                else:
                    self.log_error("设备发现", data.get('message', '未知错误'))
                    return False
            else:
                self.log_error("设备发现", f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_error("设备发现", f"请求失败: {str(e)}")
            return False
    
    def test_web_interface(self) -> bool:
        """测试Web界面"""
        try:
            response = self.session.get(self.base_url, timeout=5)
            
            if response.status_code == 200:
                if "智能推荐系统" in response.text:
                    self.log_success("Web界面", "页面加载正常")
                    return True
                else:
                    self.log_error("Web界面", "页面内容异常")
                    return False
            else:
                self.log_error("Web界面", f"状态码: {response.status_code}")
                return False
                
        except Exception as e:
            self.log_error("Web界面", f"请求失败: {str(e)}")
            return False
    
    def log_success(self, test_name: str, message: str):
        """记录成功测试"""
        result = f"✅ {test_name}: {message}"
        print(result)
        self.test_results.append(("PASS", test_name, message))
    
    def log_error(self, test_name: str, message: str):
        """记录失败测试"""
        result = f"❌ {test_name}: {message}"
        print(result)
        self.test_results.append(("FAIL", test_name, message))
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始系统功能测试...")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("Web界面", self.test_web_interface),
            ("推荐接口", self.test_recommendations),
            ("反馈接口", self.test_feedback),
            ("网络状态", self.test_network_status),
            ("设备发现", self.test_device_discovery),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
            time.sleep(1)  # 避免请求过快
        
        # 显示测试总结
        print("\n" + "=" * 50)
        print("📊 测试总结")
        print("=" * 50)
        print(f"总测试数: {total}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {total - passed}")
        print(f"成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！系统运行正常。")
            return True
        else:
            print(f"\n⚠️  有 {total - passed} 个测试失败，请检查系统配置。")
            return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="智能推荐系统测试工具")
    parser.add_argument(
        "--url", 
        default="http://localhost:5000",
        help="系统URL地址 (默认: http://localhost:5000)"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="快速测试 (跳过设备发现)"
    )
    
    args = parser.parse_args()
    
    print(f"🎯 测试目标: {args.url}")
    
    tester = SystemTester(args.url)
    
    if args.quick:
        # 快速测试，跳过耗时的设备发现
        print("⚡ 快速测试模式")
        tests = [
            ("健康检查", tester.test_health_check),
            ("Web界面", tester.test_web_interface),
            ("推荐接口", tester.test_recommendations),
            ("反馈接口", tester.test_feedback),
            ("网络状态", tester.test_network_status),
        ]
        
        passed = 0
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            if test_func():
                passed += 1
        
        print(f"\n📊 快速测试完成: {passed}/{len(tests)} 通过")
    else:
        # 完整测试
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
